#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
浏览器自动填充信息设置助手
帮助用户在浏览器中设置自动填充信息
"""

import os
import sys
import time
import logging
from DrissionPage import ChromiumOptions, ChromiumPage

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[logging.StreamHandler()]
)

# 从卡消息.txt读取支付信息
def load_payment_info():
    """从卡消息.txt文件加载支付信息"""
    payment_info = {}
    try:
        with open("卡消息.txt", "r", encoding="utf-8") as f:
            lines = f.readlines()
            
        for line in lines:
            line = line.strip()
            if "卡号：" in line:
                payment_info["card_number"] = line.split("：")[1].strip()
            elif "有效期：" in line:
                payment_info["expiry_date"] = line.split("：")[1].strip()
            elif "bin：" in line:
                payment_info["cvc"] = line.split("：")[1].strip()
            elif "名字：" in line:
                payment_info["name"] = line.split("：")[1].strip()
            elif "国家：" in line:
                payment_info["country"] = line.split("：")[1].strip()
            elif "第一行地址：" in line:
                payment_info["address_line1"] = line.split("：")[1].strip()
            elif "城市：" in line:
                payment_info["city"] = line.split("：")[1].strip()
            elif "州：" in line:
                payment_info["state"] = line.split("：")[1].strip()
            elif "邮编：" in line:
                payment_info["postal_code"] = line.split("：")[1].strip()
                
        return payment_info
    except Exception as e:
        logging.error(f"读取卡消息.txt失败: {e}")
        return None

def show_setup_instructions(payment_info):
    """显示设置说明"""
    print("\n" + "="*70)
    print("🔧 浏览器自动填充设置助手")
    print("="*70)
    print("本工具将帮助您在浏览器中设置自动填充信息")
    print("设置完成后，在支付页面点击输入框时会自动显示填充选项")
    print("\n📋 将要设置的信息：")
    print("-"*40)
    print("💳 支付信息：")
    print(f"   卡号: {payment_info.get('card_number', 'N/A')}")
    print(f"   有效期: {payment_info.get('expiry_date', 'N/A')}")
    print(f"   CVC: {payment_info.get('cvc', 'N/A')}")
    print(f"   持卡人: {payment_info.get('name', 'N/A')}")
    print("\n🏠 地址信息：")
    print(f"   姓名: {payment_info.get('name', 'N/A')}")
    print(f"   地址: {payment_info.get('address_line1', 'N/A')}")
    print(f"   城市: {payment_info.get('city', 'N/A')}")
    print(f"   州/省: {payment_info.get('state', 'N/A')}")
    print(f"   邮编: {payment_info.get('postal_code', 'N/A')}")
    print(f"   国家: {payment_info.get('country', 'N/A')}")
    print("="*70)

def open_chrome_settings():
    """打开Chrome设置页面"""
    try:
        logging.info("正在启动浏览器...")
        
        # 初始化浏览器
        co = ChromiumOptions()
        co.auto_port()
        browser = ChromiumPage(co)
        
        print("\n🌐 正在打开浏览器设置页面...")
        
        # 打开支付方式设置页面
        browser.get("chrome://settings/payments")
        print("✅ 已打开支付方式设置页面")
        
        # 等待用户操作
        print("\n📝 请按照以下步骤操作：")
        print("1. 点击'添加'按钮添加付款方式")
        print("2. 填写上面显示的支付信息")
        print("3. 保存支付信息")
        
        input("\n按回车键继续设置地址信息...")
        
        # 打开地址设置页面
        browser.get("chrome://settings/addresses")
        print("✅ 已打开地址设置页面")
        
        print("\n📝 请继续操作：")
        print("1. 点击'添加'按钮添加地址")
        print("2. 填写上面显示的地址信息")
        print("3. 保存地址信息")
        
        print("\n⏳ 设置完成后，浏览器将保持打开状态60秒...")
        print("您可以验证设置是否正确，或进行其他操作")
        
        # 保持浏览器打开
        time.sleep(60)
        
        return browser
        
    except Exception as e:
        logging.error(f"打开设置页面失败: {e}")
        return None

def main():
    """主函数"""
    try:
        # 加载支付信息
        payment_info = load_payment_info()
        if not payment_info:
            print("❌ 无法读取支付信息，请检查卡消息.txt文件")
            return
        
        # 显示设置说明
        show_setup_instructions(payment_info)
        
        # 询问是否继续
        response = input("\n是否要打开浏览器设置页面？(y/n): ").strip().lower()
        if response not in ['y', 'yes', '是']:
            print("操作已取消")
            return
        
        # 打开浏览器设置
        browser = open_chrome_settings()
        
        if browser:
            print("\n✅ 设置助手执行完成！")
            print("💡 提示：设置完成后，运行主脚本时会优先使用自动填充功能")
            
            try:
                browser.quit()
            except:
                pass
        
    except KeyboardInterrupt:
        print("\n操作已取消")
    except Exception as e:
        logging.error(f"程序执行出错: {e}")

if __name__ == "__main__":
    main()

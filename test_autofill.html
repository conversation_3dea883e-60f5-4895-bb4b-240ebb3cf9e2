
        <!DOCTYPE html>
        <html>
        <head>
            <title>自动填充测试页面</title>
            <style>
                body { font-family: Arial, sans-serif; padding: 20px; }
                .form-group { margin: 10px 0; }
                label { display: block; margin-bottom: 5px; }
                input { padding: 8px; width: 300px; border: 1px solid #ccc; }
                .section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; }
            </style>
        </head>
        <body>
            <h1>自动填充测试页面</h1>
            
            <div class="section">
                <h2>支付信息</h2>
                <div class="form-group">
                    <label>信用卡号:</label>
                    <input type="text" name="cardnumber" autocomplete="cc-number" placeholder="输入信用卡号">
                </div>
                <div class="form-group">
                    <label>有效期:</label>
                    <input type="text" name="expiry" autocomplete="cc-exp" placeholder="MM/YY">
                </div>
                <div class="form-group">
                    <label>CVC:</label>
                    <input type="text" name="cvc" autocomplete="cc-csc" placeholder="CVC">
                </div>
                <div class="form-group">
                    <label>持卡人姓名:</label>
                    <input type="text" name="cardholder" autocomplete="cc-name" placeholder="持卡人姓名">
                </div>
            </div>
            
            <div class="section">
                <h2>地址信息</h2>
                <div class="form-group">
                    <label>姓名:</label>
                    <input type="text" name="name" autocomplete="name" placeholder="姓名">
                </div>
                <div class="form-group">
                    <label>地址:</label>
                    <input type="text" name="address" autocomplete="address-line1" placeholder="地址">
                </div>
                <div class="form-group">
                    <label>城市:</label>
                    <input type="text" name="city" autocomplete="address-level2" placeholder="城市">
                </div>
                <div class="form-group">
                    <label>州/省:</label>
                    <input type="text" name="state" autocomplete="address-level1" placeholder="州/省">
                </div>
                <div class="form-group">
                    <label>邮编:</label>
                    <input type="text" name="postal" autocomplete="postal-code" placeholder="邮编">
                </div>
                <div class="form-group">
                    <label>国家:</label>
                    <input type="text" name="country" autocomplete="country" placeholder="国家">
                </div>
            </div>
            
            <div class="section">
                <h3>测试说明:</h3>
                <p>1. 如果您已在浏览器中设置了自动填充信息，点击任意输入框应该会显示自动填充选项</p>
                <p>2. 选择自动填充选项后，相关字段应该会自动填写</p>
                <p>3. 这个测试页面将保持打开60秒，您可以测试各个字段的自动填充功能</p>
            </div>
        </body>
        </html>
        
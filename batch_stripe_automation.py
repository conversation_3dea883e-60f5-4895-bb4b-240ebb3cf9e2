#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
批量Stripe支付表单自动化处理脚本
支持并发处理多个支付链接
"""

import os
import time
import logging
import threading
from concurrent.futures import ThreadPoolExecutor, as_completed
from stripe_drission_automation import init_browser_with_patch, setup_browser_autofill, fill_stripe_form_drission

# ==================== 配置参数 ====================
# 在这里直接修改参数，无需命令行
PAYMENT_FILE = '绑卡.txt'          # 支付链接文件路径
MAX_WORKERS = 2                    # 并发工作线程数 (推荐2-3个)
TASK_DELAY = 0                     # 任务间延迟秒数
AUTO_START = True                  # 是否自动开始处理（True=自动，False=需要按回车确认）
# ================================================

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - [线程%(thread)d] - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('batch_automation.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)

class StripeAutomationWorker:
    """Stripe自动化工作线程"""
    
    def __init__(self, worker_id, email, payment_url):
        self.worker_id = worker_id
        self.email = email
        self.payment_url = payment_url
        self.browser = None
        self.success = False
        self.error_message = ""
    
    def process_payment(self):
        """处理单个支付链接"""
        try:
            logging.info(f"工作线程 {self.worker_id} 开始处理: {self.email}")
            
            # 初始化浏览器
            self.browser = init_browser_with_patch()
            if not self.browser:
                raise Exception("浏览器初始化失败")
            
            # 设置自动填充功能
            if not setup_browser_autofill(self.browser):
                raise Exception("自动填充设置失败")
            
            # 打开支付页面
            logging.info(f"工作线程 {self.worker_id} 正在打开支付页面...")
            self.browser.get(self.payment_url)
            
            # 等待页面加载
            time.sleep(3)
            
            # 填写表单
            logging.info(f"工作线程 {self.worker_id} 开始填写表单...")
            if fill_stripe_form_drission(self.browser):
                self.success = True
                logging.info(f"✅ 工作线程 {self.worker_id} 处理成功: {self.email}")
            else:
                raise Exception("表单填写失败")
                
        except Exception as e:
            self.error_message = str(e)
            logging.error(f"❌ 工作线程 {self.worker_id} 处理失败: {self.email} - {e}")
        
        finally:
            # 清理浏览器资源
            if self.browser:
                try:
                    self.browser.quit()
                except:
                    pass
    
    def get_result(self):
        """获取处理结果"""
        return {
            'worker_id': self.worker_id,
            'email': self.email,
            'success': self.success,
            'error': self.error_message
        }

def load_payment_links(file_path):
    """从文件加载支付链接"""
    payment_links = []
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            for line_num, line in enumerate(f, 1):
                line = line.strip()
                if line and '----' in line:
                    parts = line.split('----', 1)
                    if len(parts) == 2:
                        email = parts[0].strip()
                        url = parts[1].strip()
                        if email and url:
                            payment_links.append((email, url))
                        else:
                            logging.warning(f"第 {line_num} 行格式不完整，跳过")
                    else:
                        logging.warning(f"第 {line_num} 行格式错误，跳过")
                elif line:
                    logging.warning(f"第 {line_num} 行格式错误，跳过")
        
        logging.info(f"成功加载 {len(payment_links)} 个支付链接")
        return payment_links
        
    except FileNotFoundError:
        logging.error(f"文件 {file_path} 不存在")
        return []
    except Exception as e:
        logging.error(f"读取文件失败: {e}")
        return []

def process_payments_batch(payment_links, max_workers=2):
    """批量处理支付链接"""
    if not payment_links:
        logging.error("没有可处理的支付链接")
        return []
    
    logging.info(f"开始批量处理 {len(payment_links)} 个支付链接，并发数: {max_workers}")
    
    results = []
    
    # 使用线程池执行器
    with ThreadPoolExecutor(max_workers=max_workers) as executor:
        # 提交所有任务
        future_to_worker = {}
        for i, (email, url) in enumerate(payment_links):
            worker = StripeAutomationWorker(i+1, email, url)
            future = executor.submit(worker.process_payment)
            future_to_worker[future] = worker
        
        # 等待任务完成
        for future in as_completed(future_to_worker):
            worker = future_to_worker[future]
            try:
                future.result()  # 获取结果，如果有异常会抛出
            except Exception as e:
                logging.error(f"工作线程 {worker.worker_id} 执行异常: {e}")
            
            # 收集结果
            result = worker.get_result()
            results.append(result)
            
            # 实时显示进度
            completed = len(results)
            total = len(payment_links)
            success_count = sum(1 for r in results if r['success'])
            logging.info(f"进度: {completed}/{total} 完成，成功: {success_count}")
    
    return results

def print_summary(results):
    """打印处理结果摘要"""
    if not results:
        print("没有处理结果")
        return
    
    total = len(results)
    success_count = sum(1 for r in results if r['success'])
    failed_count = total - success_count
    
    print(f"\n{'='*50}")
    print(f"批量处理结果摘要")
    print(f"{'='*50}")
    print(f"总数: {total}")
    print(f"成功: {success_count}")
    print(f"失败: {failed_count}")
    print(f"成功率: {success_count/total*100:.1f}%")
    
    if failed_count > 0:
        print(f"\n失败的任务:")
        for result in results:
            if not result['success']:
                print(f"  - {result['email']}: {result['error']}")

def main():
    """主函数"""
    print(f"批量Stripe支付自动化处理工具")
    print(f"文件: {PAYMENT_FILE}")
    print(f"并发数: {MAX_WORKERS}")
    print(f"延迟: {TASK_DELAY}秒")
    print(f"自动开始: {'是' if AUTO_START else '否'}")
    print("-" * 50)

    # 加载支付链接
    payment_links = load_payment_links(PAYMENT_FILE)
    if not payment_links:
        print("没有找到有效的支付链接，程序退出")
        return

    # 显示将要处理的链接
    print(f"将要处理的支付链接:")
    for i, (email, url) in enumerate(payment_links, 1):
        print(f"  {i}. {email}")

    # 确认开始处理
    if not AUTO_START:
        input("\n按回车键开始处理...")
    else:
        print("\n自动开始处理...")
        time.sleep(1)

    # 开始处理
    start_time = time.time()
    results = process_payments_batch(payment_links, MAX_WORKERS)
    end_time = time.time()
    
    # 显示结果
    print_summary(results)
    print(f"\n总耗时: {end_time - start_time:.2f} 秒")
    
    # 保存详细结果到文件
    with open('batch_results.txt', 'w', encoding='utf-8') as f:
        f.write(f"批量处理结果 - {time.strftime('%Y-%m-%d %H:%M:%S')}\n")
        f.write(f"{'='*50}\n")
        for result in results:
            status = "成功" if result['success'] else f"失败: {result['error']}"
            f.write(f"{result['email']} - {status}\n")
    
    print(f"\n详细结果已保存到: batch_results.txt")
    print(f"日志文件: batch_automation.log")

if __name__ == "__main__":
    main()

# 批量处理配置说明

## 快速配置

打开 `batch_stripe_automation.py` 文件，在顶部找到配置区域：

```python
# ==================== 配置参数 ====================
# 在这里直接修改参数，无需命令行
PAYMENT_FILE = '绑卡.txt'          # 支付链接文件路径
MAX_WORKERS = 2                    # 并发工作线程数 (推荐2-3个)
TASK_DELAY = 0                     # 任务间延迟秒数
AUTO_START = True                  # 是否自动开始处理（True=自动，False=需要按回车确认）
# ================================================
```

## 参数说明

### 1. PAYMENT_FILE (支付链接文件)
- **默认**: `'绑卡.txt'`
- **说明**: 包含邮箱和支付链接的文件路径
- **格式**: 每行一个 `邮箱----支付链接`

### 2. MAX_WORKERS (并发数)
- **默认**: `2`
- **推荐设置**:
  - `1`: 单线程，最稳定，适合调试
  - `2`: 双线程，平衡性能和稳定性
  - `3`: 三线程，较快速度
  - `4-5`: 高速处理，需要好的硬件

### 3. TASK_DELAY (任务延迟)
- **默认**: `0`
- **说明**: 每个任务完成后等待的秒数
- **建议**: 网络不稳定时设置为 `2-5` 秒

### 4. AUTO_START (自动开始)
- **默认**: `True`
- **True**: 自动开始处理，无需确认
- **False**: 需要按回车键确认后开始

## 常用配置示例

### 稳定模式（推荐）
```python
MAX_WORKERS = 2
TASK_DELAY = 1
AUTO_START = True
```

### 高速模式
```python
MAX_WORKERS = 4
TASK_DELAY = 0
AUTO_START = True
```

### 调试模式
```python
MAX_WORKERS = 1
TASK_DELAY = 2
AUTO_START = False
```

### 谨慎模式（网络不稳定时）
```python
MAX_WORKERS = 1
TASK_DELAY = 5
AUTO_START = False
```

## 使用步骤

1. **准备链接文件**: 在 `绑卡.txt` 中添加邮箱和链接
2. **修改配置**: 根据需要调整上述参数
3. **运行脚本**: `python batch_stripe_automation.py`
4. **查看结果**: 检查 `batch_results.txt` 和日志

## 输出文件

- **batch_results.txt**: 处理结果摘要
- **batch_automation.log**: 详细运行日志

## 性能参考

- **2个并发**: 约每分钟处理4-6个链接
- **3个并发**: 约每分钟处理6-9个链接
- **4个并发**: 约每分钟处理8-12个链接

*实际速度取决于网络状况和页面复杂度*

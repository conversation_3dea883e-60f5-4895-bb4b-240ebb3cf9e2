#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
自动填充功能测试脚本
用于测试浏览器自动填充是否正常工作
"""

import time
import logging
from DrissionPage import ChromiumOptions, ChromiumPage

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[logging.StreamHandler()]
)

def test_autofill():
    """测试自动填充功能"""
    try:
        logging.info("=== 自动填充功能测试 ===")
        
        # 初始化浏览器
        co = ChromiumOptions()
        co.auto_port()
        browser = ChromiumPage(co)
        
        # 打开一个包含表单的测试页面
        test_html = """
        <!DOCTYPE html>
        <html>
        <head>
            <title>自动填充测试页面</title>
            <style>
                body { font-family: Arial, sans-serif; padding: 20px; }
                .form-group { margin: 10px 0; }
                label { display: block; margin-bottom: 5px; }
                input { padding: 8px; width: 300px; border: 1px solid #ccc; }
                .section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; }
            </style>
        </head>
        <body>
            <h1>自动填充测试页面</h1>
            
            <div class="section">
                <h2>支付信息</h2>
                <div class="form-group">
                    <label>信用卡号:</label>
                    <input type="text" name="cardnumber" autocomplete="cc-number" placeholder="输入信用卡号">
                </div>
                <div class="form-group">
                    <label>有效期:</label>
                    <input type="text" name="expiry" autocomplete="cc-exp" placeholder="MM/YY">
                </div>
                <div class="form-group">
                    <label>CVC:</label>
                    <input type="text" name="cvc" autocomplete="cc-csc" placeholder="CVC">
                </div>
                <div class="form-group">
                    <label>持卡人姓名:</label>
                    <input type="text" name="cardholder" autocomplete="cc-name" placeholder="持卡人姓名">
                </div>
            </div>
            
            <div class="section">
                <h2>地址信息</h2>
                <div class="form-group">
                    <label>姓名:</label>
                    <input type="text" name="name" autocomplete="name" placeholder="姓名">
                </div>
                <div class="form-group">
                    <label>地址:</label>
                    <input type="text" name="address" autocomplete="address-line1" placeholder="地址">
                </div>
                <div class="form-group">
                    <label>城市:</label>
                    <input type="text" name="city" autocomplete="address-level2" placeholder="城市">
                </div>
                <div class="form-group">
                    <label>州/省:</label>
                    <input type="text" name="state" autocomplete="address-level1" placeholder="州/省">
                </div>
                <div class="form-group">
                    <label>邮编:</label>
                    <input type="text" name="postal" autocomplete="postal-code" placeholder="邮编">
                </div>
                <div class="form-group">
                    <label>国家:</label>
                    <input type="text" name="country" autocomplete="country" placeholder="国家">
                </div>
            </div>
            
            <div class="section">
                <h3>测试说明:</h3>
                <p>1. 如果您已在浏览器中设置了自动填充信息，点击任意输入框应该会显示自动填充选项</p>
                <p>2. 选择自动填充选项后，相关字段应该会自动填写</p>
                <p>3. 这个测试页面将保持打开60秒，您可以测试各个字段的自动填充功能</p>
            </div>
        </body>
        </html>
        """
        
        # 创建临时HTML文件
        with open("test_autofill.html", "w", encoding="utf-8") as f:
            f.write(test_html)
        
        # 打开测试页面
        import os
        test_url = f"file:///{os.path.abspath('test_autofill.html')}"
        browser.get(test_url)
        
        logging.info("✅ 测试页面已打开")
        logging.info("📝 请在浏览器中测试自动填充功能:")
        logging.info("   1. 点击任意输入框")
        logging.info("   2. 查看是否出现自动填充选项")
        logging.info("   3. 选择自动填充选项测试效果")
        
        # 自动测试第一个字段
        logging.info("\n🤖 自动测试信用卡号字段...")
        try:
            card_input = browser.ele('input[name="cardnumber"]', timeout=5)
            if card_input:
                card_input.click()
                time.sleep(2)
                
                # 检查是否有值
                value = card_input.attr('value')
                if value and value.strip():
                    logging.info(f"✅ 检测到自动填充: {value}")
                else:
                    logging.info("ℹ️ 未检测到自动填充，可能需要手动设置")
        except Exception as e:
            logging.warning(f"自动测试失败: {e}")
        
        # 保持页面打开
        logging.info("\n⏳ 页面将保持打开60秒，您可以手动测试其他字段...")
        for i in range(60, 0, -5):
            logging.info(f"剩余时间: {i} 秒")
            time.sleep(5)
        
        # 清理
        browser.quit()
        try:
            os.remove("test_autofill.html")
        except:
            pass
            
        logging.info("✅ 测试完成")
        
    except Exception as e:
        logging.error(f"测试失败: {e}")

def main():
    """主函数"""
    print("🧪 自动填充功能测试工具")
    print("="*50)
    print("此工具将打开一个测试页面，帮助您验证浏览器自动填充功能是否正常工作")
    print("\n如果您还没有设置浏览器自动填充，请先运行: python setup_autofill.py")
    
    response = input("\n是否开始测试？(y/n): ").strip().lower()
    if response in ['y', 'yes', '是']:
        test_autofill()
    else:
        print("测试已取消")

if __name__ == "__main__":
    main()

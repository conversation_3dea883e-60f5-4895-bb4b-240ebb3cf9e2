#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
内置自动填充功能测试脚本
测试脚本注入的自动填充功能是否正常工作
"""

import time
import logging
from DrissionPage import ChromiumOptions, ChromiumPage

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[logging.StreamHandler()]
)

# 测试用的支付信息
PAYMENT_INFO = {
    "card_number": "4937 2420 1027 3546",
    "expiry_date": "06/28",
    "cvc": "124",
    "name": "Canestro",
    "country": "Canada",
    "address_line1": "2225 137th Avenue",
    "city": "Edmonton",
    "state": "Alberta",
    "postal_code": "T5J 3P4"
}

def setup_browser_autofill(browser):
    """在浏览器中设置自动填充信息"""
    try:
        logging.info("正在设置浏览器自动填充信息...")

        # 注入自动填充数据到浏览器
        autofill_script = f"""
        // 创建自动填充数据
        const paymentData = {{
            cardNumber: '{PAYMENT_INFO["card_number"]}',
            expiryDate: '{PAYMENT_INFO["expiry_date"]}',
            cvc: '{PAYMENT_INFO["cvc"]}',
            name: '{PAYMENT_INFO["name"]}',
            country: '{PAYMENT_INFO["country"]}',
            addressLine1: '{PAYMENT_INFO["address_line1"]}',
            city: '{PAYMENT_INFO["city"]}',
            state: '{PAYMENT_INFO["state"]}',
            postalCode: '{PAYMENT_INFO["postal_code"]}'
        }};

        // 存储到 sessionStorage 供后续使用
        sessionStorage.setItem('autoFillData', JSON.stringify(paymentData));

        // 创建自动填充函数
        window.autoFillForm = function(element) {{
            const data = JSON.parse(sessionStorage.getItem('autoFillData') || '{{}}');
            const fieldName = element.name || element.id || element.getAttribute('autocomplete') || '';

            // 根据字段特征自动填充
            if (fieldName.includes('card') || fieldName.includes('number') || element.getAttribute('autocomplete') === 'cc-number') {{
                element.value = data.cardNumber;
                element.dispatchEvent(new Event('input', {{ bubbles: true }}));
                element.dispatchEvent(new Event('change', {{ bubbles: true }}));
                return true;
            }} else if (fieldName.includes('expiry') || fieldName.includes('exp') || element.getAttribute('autocomplete') === 'cc-exp') {{
                element.value = data.expiryDate;
                element.dispatchEvent(new Event('input', {{ bubbles: true }}));
                element.dispatchEvent(new Event('change', {{ bubbles: true }}));
                return true;
            }} else if (fieldName.includes('cvc') || fieldName.includes('cvv') || element.getAttribute('autocomplete') === 'cc-csc') {{
                element.value = data.cvc;
                element.dispatchEvent(new Event('input', {{ bubbles: true }}));
                element.dispatchEvent(new Event('change', {{ bubbles: true }}));
                return true;
            }} else if (fieldName.includes('name') || element.getAttribute('autocomplete') === 'cc-name') {{
                element.value = data.name;
                element.dispatchEvent(new Event('input', {{ bubbles: true }}));
                element.dispatchEvent(new Event('change', {{ bubbles: true }}));
                return true;
            }} else if (fieldName.includes('address') || element.getAttribute('autocomplete') === 'address-line1') {{
                element.value = data.addressLine1;
                element.dispatchEvent(new Event('input', {{ bubbles: true }}));
                element.dispatchEvent(new Event('change', {{ bubbles: true }}));
                return true;
            }} else if (fieldName.includes('city') || element.getAttribute('autocomplete') === 'address-level2') {{
                element.value = data.city;
                element.dispatchEvent(new Event('input', {{ bubbles: true }}));
                element.dispatchEvent(new Event('change', {{ bubbles: true }}));
                return true;
            }} else if (fieldName.includes('state') || fieldName.includes('province') || element.getAttribute('autocomplete') === 'address-level1') {{
                element.value = data.state;
                element.dispatchEvent(new Event('input', {{ bubbles: true }}));
                element.dispatchEvent(new Event('change', {{ bubbles: true }}));
                return true;
            }} else if (fieldName.includes('postal') || fieldName.includes('zip') || element.getAttribute('autocomplete') === 'postal-code') {{
                element.value = data.postalCode;
                element.dispatchEvent(new Event('input', {{ bubbles: true }}));
                element.dispatchEvent(new Event('change', {{ bubbles: true }}));
                return true;
            }}
            return false;
        }};

        console.log('自动填充功能已设置');
        """

        browser.run_js(autofill_script)
        logging.info("✅ 浏览器自动填充信息设置完成")
        return True

    except Exception as e:
        logging.error(f"设置自动填充失败: {e}")
        return False

def test_autofill():
    """测试自动填充功能"""
    try:
        logging.info("=== 内置自动填充功能测试 ===")

        # 初始化浏览器
        co = ChromiumOptions()
        co.auto_port()
        browser = ChromiumPage(co)
        
        # 打开一个包含表单的测试页面
        test_html = """
        <!DOCTYPE html>
        <html>
        <head>
            <title>自动填充测试页面</title>
            <style>
                body { font-family: Arial, sans-serif; padding: 20px; }
                .form-group { margin: 10px 0; }
                label { display: block; margin-bottom: 5px; }
                input { padding: 8px; width: 300px; border: 1px solid #ccc; }
                .section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; }
            </style>
        </head>
        <body>
            <h1>自动填充测试页面</h1>
            
            <div class="section">
                <h2>支付信息</h2>
                <div class="form-group">
                    <label>信用卡号:</label>
                    <input type="text" name="cardnumber" autocomplete="cc-number" placeholder="输入信用卡号">
                </div>
                <div class="form-group">
                    <label>有效期:</label>
                    <input type="text" name="expiry" autocomplete="cc-exp" placeholder="MM/YY">
                </div>
                <div class="form-group">
                    <label>CVC:</label>
                    <input type="text" name="cvc" autocomplete="cc-csc" placeholder="CVC">
                </div>
                <div class="form-group">
                    <label>持卡人姓名:</label>
                    <input type="text" name="cardholder" autocomplete="cc-name" placeholder="持卡人姓名">
                </div>
            </div>
            
            <div class="section">
                <h2>地址信息</h2>
                <div class="form-group">
                    <label>姓名:</label>
                    <input type="text" name="name" autocomplete="name" placeholder="姓名">
                </div>
                <div class="form-group">
                    <label>地址:</label>
                    <input type="text" name="address" autocomplete="address-line1" placeholder="地址">
                </div>
                <div class="form-group">
                    <label>城市:</label>
                    <input type="text" name="city" autocomplete="address-level2" placeholder="城市">
                </div>
                <div class="form-group">
                    <label>州/省:</label>
                    <input type="text" name="state" autocomplete="address-level1" placeholder="州/省">
                </div>
                <div class="form-group">
                    <label>邮编:</label>
                    <input type="text" name="postal" autocomplete="postal-code" placeholder="邮编">
                </div>
                <div class="form-group">
                    <label>国家:</label>
                    <input type="text" name="country" autocomplete="country" placeholder="国家">
                </div>
            </div>
            
            <div class="section">
                <h3>测试说明:</h3>
                <p>1. 如果您已在浏览器中设置了自动填充信息，点击任意输入框应该会显示自动填充选项</p>
                <p>2. 选择自动填充选项后，相关字段应该会自动填写</p>
                <p>3. 这个测试页面将保持打开60秒，您可以测试各个字段的自动填充功能</p>
            </div>
        </body>
        </html>
        """
        
        # 创建临时HTML文件
        with open("test_autofill.html", "w", encoding="utf-8") as f:
            f.write(test_html)
        
        # 打开测试页面
        import os
        test_url = f"file:///{os.path.abspath('test_autofill.html')}"
        browser.get(test_url)

        # 设置自动填充功能
        setup_browser_autofill(browser)

        logging.info("✅ 测试页面已打开，自动填充功能已设置")

        # 自动测试各个字段
        test_fields = [
            ('input[name="cardnumber"]', '信用卡号'),
            ('input[name="expiry"]', '有效期'),
            ('input[name="cvc"]', 'CVC'),
            ('input[name="cardholder"]', '持卡人姓名'),
            ('input[name="name"]', '姓名'),
            ('input[name="address"]', '地址'),
            ('input[name="city"]', '城市'),
            ('input[name="state"]', '州/省'),
            ('input[name="postal"]', '邮编'),
            ('input[name="country"]', '国家')
        ]

        logging.info("\n🤖 开始自动测试各个字段...")
        for selector, field_name in test_fields:
            try:
                element = browser.ele(selector, timeout=3)
                if element:
                    logging.info(f"测试 {field_name} 字段...")
                    element.click()
                    time.sleep(0.5)

                    # 使用注入的自动填充函数
                    result = browser.run_js("""
                        const element = arguments[0];
                        if (window.autoFillForm && typeof window.autoFillForm === 'function') {
                            return window.autoFillForm(element);
                        }
                        return false;
                    """, element)

                    if result:
                        value = element.attr('value')
                        logging.info(f"✅ {field_name} 自动填充成功: {value}")
                    else:
                        logging.info(f"⚠️ {field_name} 自动填充失败")

            except Exception as e:
                logging.warning(f"测试 {field_name} 时出错: {e}")

        # 保持页面打开
        logging.info("\n⏳ 页面将保持打开30秒，您可以手动验证结果...")
        for i in range(30, 0, -5):
            logging.info(f"剩余时间: {i} 秒")
            time.sleep(5)
        
        # 清理
        browser.quit()
        try:
            os.remove("test_autofill.html")
        except:
            pass
            
        logging.info("✅ 测试完成")
        
    except Exception as e:
        logging.error(f"测试失败: {e}")

def main():
    """主函数"""
    print("🧪 内置自动填充功能测试工具")
    print("="*50)
    print("此工具将测试脚本内置的自动填充功能")
    print("无需手动设置浏览器，脚本会自动注入填充数据")

    response = input("\n是否开始测试？(y/n): ").strip().lower()
    if response in ['y', 'yes', '是']:
        test_autofill()
    else:
        print("测试已取消")

if __name__ == "__main__":
    main()

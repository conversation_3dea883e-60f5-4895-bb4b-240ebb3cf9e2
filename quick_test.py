#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
快速测试自动填充功能
"""

import time
import logging
from DrissionPage import ChromiumOptions, ChromiumPage

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

# 测试用的支付信息
PAYMENT_INFO = {
    "card_number": "4937 2420 1027 3546",
    "expiry_date": "06/28",
    "cvc": "124",
    "name": "Canestro",
    "country": "Canada",
    "address_line1": "2225 137th Avenue",
    "city": "Edmonton",
    "state": "Alberta",
    "postal_code": "T5J 3P4"
}

def setup_browser_autofill(browser):
    """在浏览器中设置自动填充信息"""
    try:
        logging.info("正在设置浏览器自动填充信息...")
        
        # 注入自动填充数据到浏览器
        autofill_script = f"""
        // 创建自动填充数据
        const paymentData = {{
            cardNumber: '{PAYMENT_INFO["card_number"]}',
            expiryDate: '{PAYMENT_INFO["expiry_date"]}',
            cvc: '{PAYMENT_INFO["cvc"]}',
            name: '{PAYMENT_INFO["name"]}',
            country: '{PAYMENT_INFO["country"]}',
            addressLine1: '{PAYMENT_INFO["address_line1"]}',
            city: '{PAYMENT_INFO["city"]}',
            state: '{PAYMENT_INFO["state"]}',
            postalCode: '{PAYMENT_INFO["postal_code"]}'
        }};
        
        // 存储到 sessionStorage 供后续使用
        sessionStorage.setItem('autoFillData', JSON.stringify(paymentData));
        
        // 创建自动填充函数
        window.autoFillForm = function(element) {{
            const data = JSON.parse(sessionStorage.getItem('autoFillData') || '{{}}');
            const fieldName = (element.name || element.id || element.getAttribute('autocomplete') || '').toLowerCase();
            
            console.log('AutoFill - fieldName:', fieldName, 'data:', data);
            
            // 根据字段特征自动填充
            if (fieldName.includes('card') || fieldName.includes('number') || 
                element.getAttribute('autocomplete') === 'cc-number' ||
                fieldName === 'cardnumber') {{
                element.value = data.cardNumber;
                element.dispatchEvent(new Event('input', {{ bubbles: true }}));
                element.dispatchEvent(new Event('change', {{ bubbles: true }}));
                console.log('Filled card number:', data.cardNumber);
                return true;
            }} else if (fieldName.includes('expiry') || fieldName.includes('exp') || 
                       element.getAttribute('autocomplete') === 'cc-exp' ||
                       fieldName === 'expiry') {{
                element.value = data.expiryDate;
                element.dispatchEvent(new Event('input', {{ bubbles: true }}));
                element.dispatchEvent(new Event('change', {{ bubbles: true }}));
                console.log('Filled expiry:', data.expiryDate);
                return true;
            }} else if (fieldName.includes('cvc') || fieldName.includes('cvv') || 
                       element.getAttribute('autocomplete') === 'cc-csc' ||
                       fieldName === 'cvc') {{
                element.value = data.cvc;
                element.dispatchEvent(new Event('input', {{ bubbles: true }}));
                element.dispatchEvent(new Event('change', {{ bubbles: true }}));
                console.log('Filled CVC:', data.cvc);
                return true;
            }}
            console.log('No matching field pattern found');
            return false;
        }};
        
        console.log('自动填充功能已设置');
        """
        
        browser.run_js(autofill_script)
        logging.info("✅ 浏览器自动填充信息设置完成")
        return True
        
    except Exception as e:
        logging.error(f"设置自动填充失败: {e}")
        return False

def main():
    """主函数"""
    try:
        logging.info("=== 快速自动填充测试 ===")
        
        # 初始化浏览器
        co = ChromiumOptions()
        co.auto_port()
        browser = ChromiumPage(co)
        
        # 打开测试页面
        import os
        test_url = f"file:///{os.path.abspath('test_autofill.html').replace(chr(92), '/')}"
        logging.info(f"正在打开测试页面: {test_url}")
        browser.get(test_url)
        
        # 等待页面加载
        time.sleep(2)
        
        # 设置自动填充功能
        setup_browser_autofill(browser)
        
        # 测试第一个输入框（信用卡号）
        logging.info("\n🧪 测试信用卡号字段...")
        try:
            card_input = browser.ele('@name=cardnumber', timeout=5)
            if card_input:
                logging.info("✅ 找到信用卡号输入框")
                card_input.click()
                time.sleep(0.5)
                
                # 调用自动填充
                result = browser.run_js("""
                    const element = document.querySelector('input[name="cardnumber"]');
                    if (element && window.autoFillForm) {
                        const success = window.autoFillForm(element);
                        return {success: success, value: element.value, name: element.name};
                    }
                    return {success: false, value: '', name: 'not found'};
                """)
                
                logging.info(f"自动填充结果: {result}")
                
                if result.get('success'):
                    logging.info(f"🎉 自动填充成功！填充值: {result.get('value')}")
                else:
                    logging.info("❌ 自动填充失败")
            else:
                logging.warning("❌ 未找到信用卡号输入框")
                
        except Exception as e:
            logging.error(f"测试时出错: {e}")
        
        # 保持页面打开
        logging.info("\n⏳ 页面将保持打开20秒，您可以手动验证...")
        time.sleep(20)
        
        browser.quit()
        logging.info("✅ 测试完成")
        
    except Exception as e:
        logging.error(f"测试失败: {e}")

if __name__ == "__main__":
    main()

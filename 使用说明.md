# Stripe 智能自动填充支付工具使用说明

## 📋 项目概述

本工具使用 DrissionPage 自动化库，通过 JavaScript 注入实现智能自动填充功能，无需手动设置浏览器，实现 Stripe 支付表单的全自动填写。

## 🚀 主要特性

- **内置自动填充**: 脚本自动注入填充数据，无需手动设置浏览器
- **智能填充策略**: 优先使用注入的自动填充，失败时自动切换到手动填写
- **反检测机制**: 集成 turnstilePatch 扩展，模拟真实用户行为
- **人性化操作**: 随机延迟、模拟打字，更接近真实用户操作
- **完整流程**: 支持从表单填写到支付完成的全流程自动化
- **即开即用**: 每次打开全新浏览器都能自动工作

## 📁 文件结构

```
├── stripe_drission_automation.py  # 主要自动化脚本
├── setup_autofill.py             # 浏览器自动填充设置助手
├── 卡消息.txt                    # 支付信息配置文件
├── 绑卡.txt                      # 支付页面URL
├── 1.打开一个单独的实例网页/
│   ├── simple_browser.py         # 简单浏览器启动工具
│   └── turnstilePatch/           # 反检测扩展
└── 使用说明.md                   # 本文档
```

## 🔧 使用步骤

### 第一步：准备工作

1. **安装依赖**
   ```bash
   pip install DrissionPage
   ```

2. **配置支付信息**
   - 编辑 `卡消息.txt` 文件，确保信息格式正确
   - 在 `绑卡.txt` 中填入支付页面URL

### 第二步：直接运行（无需额外设置）

```bash
python stripe_drission_automation.py
```

**就这么简单！** 脚本会自动：
- 启动全新的浏览器实例
- 自动注入支付信息到浏览器
- 智能识别表单字段并自动填充
- 完成整个支付流程

### 可选：测试自动填充功能

如果想验证自动填充功能是否正常：
```bash
python test_autofill.py
```

## 💡 工作原理

### 智能填充策略

1. **JavaScript 注入**: 脚本启动时自动向浏览器注入自动填充功能
   - 将支付信息存储在浏览器的 sessionStorage 中
   - 创建智能字段识别函数
   - 支持多种字段名称和属性匹配

2. **智能字段识别**: 自动识别各种支付表单字段
   - 信用卡号：`cardnumber`, `card`, `cc-number` 等
   - 有效期：`expiry`, `exp`, `cc-exp` 等
   - CVC：`cvc`, `cvv`, `cc-csc` 等
   - 地址信息：`address`, `city`, `state`, `postal` 等

3. **双重保障**: 如果注入的自动填充失败，自动切换到手动填写模式
   - 模拟人类打字行为
   - 随机延迟增加真实性
   - 确保所有字段都被正确填写

### 反检测机制

- **扩展加载**: 自动加载 turnstilePatch 反检测扩展
- **用户代理随机化**: 使用多个真实的用户代理字符串
- **JavaScript反检测**: 修改 navigator.webdriver 等检测点
- **人性化操作**: 随机延迟、模拟鼠标移动等

## ⚙️ 配置选项

### 支付信息格式 (卡消息.txt)
```
卡号：4937 2420 1027 3546
有效期：06/28
bin：124
名字：Canestro
国家：Canada
第一行地址：2225 137th Avenue
城市：Edmonton
州：Alberta
邮编：T5J 3P4
```

### URL配置 (绑卡.txt)
```
https://your-stripe-payment-url.com
```

## 🔍 日志说明

脚本运行时会输出详细日志：
- `✅` 表示操作成功
- `⚠️` 表示警告信息
- `❌` 表示错误信息
- `🎉` 表示重要成功事件

## 🛠️ 故障排除

### 常见问题

1. **自动填充不工作**
   - 确保已在浏览器中正确设置自动填充信息
   - 检查字段名称是否匹配
   - 脚本会自动切换到手动填写模式

2. **扩展加载失败**
   - 检查 turnstilePatch 目录是否存在
   - 确保扩展文件完整

3. **页面元素找不到**
   - 支付页面可能已更新，需要调整选择器
   - 检查网络连接和页面加载状态

### 调试模式

如需调试，可以修改日志级别：
```python
logging.basicConfig(level=logging.DEBUG)
```

## 📝 注意事项

1. **合规使用**: 请确保在合法合规的场景下使用本工具
2. **测试环境**: 建议先在测试环境中验证功能
3. **信息安全**: 妥善保管支付信息，避免泄露
4. **版本兼容**: 确保 DrissionPage 版本与脚本兼容

## 🔄 更新日志

- **v2.0**: 添加浏览器自动填充支持
- **v1.0**: 基础自动化填写功能

## 📞 技术支持

如遇问题，请检查：
1. 依赖库版本
2. 浏览器版本兼容性
3. 网络连接状态
4. 配置文件格式

#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
快速测试优化后的自动填充功能
"""

import time
import logging
from stripe_drission_automation import setup_browser_autofill, trigger_autofill, try_autofill_or_manual, PAYMENT_INFO
from DrissionPage import ChromiumOptions, ChromiumPage

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

def test_fast_autofill():
    """测试快速自动填充功能"""
    try:
        logging.info("=== 快速自动填充测试 ===")
        
        # 初始化浏览器
        co = ChromiumOptions()
        co.auto_port()
        browser = ChromiumPage(co)
        
        # 打开测试页面
        import os
        test_url = f"file:///{os.path.abspath('test_autofill.html').replace(chr(92), '/')}"
        logging.info(f"正在打开测试页面: {test_url}")
        browser.get(test_url)
        
        # 等待页面加载
        time.sleep(1)
        
        # 设置自动填充功能
        setup_browser_autofill(browser)
        
        # 记录开始时间
        start_time = time.time()
        
        # 测试所有字段的快速填充
        test_fields = [
            ('@name=cardnumber', PAYMENT_INFO["card_number"], '信用卡号'),
            ('@name=expiry', PAYMENT_INFO["expiry_date"], '有效期'),
            ('@name=cvc', PAYMENT_INFO["cvc"], 'CVC'),
            ('@name=cardholder', PAYMENT_INFO["name"], '持卡人姓名'),
            ('@name=name', PAYMENT_INFO["name"], '姓名'),
            ('@name=address', PAYMENT_INFO["address_line1"], '地址'),
            ('@name=city', PAYMENT_INFO["city"], '城市'),
            ('@name=state', PAYMENT_INFO["state"], '州/省'),
            ('@name=postal', PAYMENT_INFO["postal_code"], '邮编'),
            ('@name=country', PAYMENT_INFO["country"], '国家')
        ]
        
        success_count = 0
        total_count = len(test_fields)
        
        for selector, fallback_text, field_name in test_fields:
            try:
                logging.info(f"测试 {field_name}...")
                element = browser.ele(selector, timeout=3)
                if element:
                    # 使用优化后的快速填充函数
                    if try_autofill_or_manual(browser, element, fallback_text, field_name):
                        success_count += 1
                else:
                    logging.warning(f"未找到 {field_name} 字段")
                    
            except Exception as e:
                logging.error(f"测试 {field_name} 时出错: {e}")
        
        # 计算总耗时
        end_time = time.time()
        total_time = end_time - start_time
        
        # 显示结果
        logging.info(f"\n🎉 测试完成！")
        logging.info(f"✅ 成功填充: {success_count}/{total_count} 个字段")
        logging.info(f"⏱️ 总耗时: {total_time:.2f} 秒")
        logging.info(f"⚡ 平均每字段: {total_time/total_count:.2f} 秒")
        
        if success_count == total_count:
            logging.info("🏆 所有字段填充成功！自动填充功能工作完美！")
        
        # 保持页面打开以便验证
        logging.info("\n⏳ 页面将保持打开15秒，您可以验证结果...")
        time.sleep(15)
        
        browser.quit()
        logging.info("✅ 测试完成")
        
        return success_count == total_count
        
    except Exception as e:
        logging.error(f"测试失败: {e}")
        return False

def main():
    """主函数"""
    success = test_fast_autofill()
    if success:
        print("\n🎉 快速自动填充功能测试通过！")
        print("现在可以使用优化后的主脚本进行快速支付表单填写。")
    else:
        print("\n❌ 测试失败，需要进一步调试。")

if __name__ == "__main__":
    main()

# 批量Stripe支付自动化处理工具

## 功能特点

✅ **并发处理**: 支持同时处理多个支付链接  
✅ **快速填充**: 优化后的自动填充，平均每个表单0.73秒/字段  
✅ **智能重试**: 自动处理失败重试机制  
✅ **详细日志**: 完整的处理日志和结果报告  
✅ **灵活配置**: 可调整并发数、延迟等参数  

## 使用方法

### 1. 准备支付链接文件

在 `绑卡.txt` 文件中按以下格式添加邮箱和支付链接：

```
邮箱1----支付链接1
邮箱2----支付链接2
邮箱3----支付链接3
```

**示例：**
```
<EMAIL>----https://checkout.stripe.com/c/pay/cs_live_xxxxx
<EMAIL>----https://checkout.stripe.com/c/pay/cs_live_yyyyy
```

### 2. 运行批量处理

#### 基本用法（默认2个并发）：
```bash
python batch_stripe_automation.py
```

#### 自定义并发数：
```bash
python batch_stripe_automation.py --workers 3
```

#### 指定不同的文件：
```bash
python batch_stripe_automation.py --file 我的链接.txt --workers 4
```

#### 添加任务间延迟：
```bash
python batch_stripe_automation.py --workers 2 --delay 5
```

### 3. 参数说明

| 参数 | 简写 | 默认值 | 说明 |
|------|------|--------|------|
| `--file` | `-f` | `绑卡.txt` | 支付链接文件路径 |
| `--workers` | `-w` | `2` | 并发工作线程数 |
| `--delay` | `-d` | `0` | 任务间延迟秒数 |

### 4. 并发数建议

- **2-3个并发**: 适合大多数情况，稳定性好
- **4-5个并发**: 适合处理大量链接，需要较好的网络和硬件
- **1个并发**: 适合调试或网络不稳定时使用

⚠️ **注意**: 并发数过高可能导致：
- 浏览器资源占用过多
- 网络请求被限制
- 系统不稳定

## 输出文件

处理完成后会生成以下文件：

1. **`batch_results.txt`**: 详细的处理结果
2. **`batch_automation.log`**: 完整的运行日志

## 处理结果示例

```
==================================================
批量处理结果摘要
==================================================
总数: 5
成功: 4
失败: 1
成功率: 80.0%

失败的任务:
  - <EMAIL>: 页面加载超时
```

## 常见问题

### Q: 如何提高成功率？
A: 
- 减少并发数（推荐2-3个）
- 增加任务间延迟
- 检查网络连接稳定性
- 确保支付链接有效

### Q: 处理速度慢怎么办？
A: 
- 适当增加并发数
- 检查是否有不必要的延迟
- 确保硬件资源充足

### Q: 出现浏览器错误怎么办？
A: 
- 重启程序
- 减少并发数
- 检查Chrome浏览器是否正常

## 监控和调试

### 实时监控
程序运行时会显示实时进度：
```
进度: 3/5 完成，成功: 2
```

### 日志查看
```bash
tail -f batch_automation.log
```

### 调试模式
设置并发数为1进行单线程调试：
```bash
python batch_stripe_automation.py --workers 1
```

## 安全提示

⚠️ **重要**: 
- 仅用于测试环境
- 不要在生产环境使用真实支付信息
- 遵守相关网站的使用条款
- 注意保护个人隐私信息
